# Contact Form Security Implementation

## Overview

Secure implementation of contact form submission using:

- Frontend-generated CSRF tokens
- Google reCAPTCHA v3
- Rate limiting
- Input validation

## Security Measures

### 1. CSRF Protection

```typescript
// Generate cryptographically secure token
const token = crypto.getRandomValues(new Uint8Array(32));

// Set in cookie with security flags
document.cookie = `x-csrf-token=${token}; SameSite=Strict; Secure`;

// Include in request header
headers: {
    'X-CSRF-Token'
:
    token
}
```

### 2. reCAPTCHA Integration

```typescript
// Frontend
const recaptchaToken = await grecaptcha.execute('SITE_KEY', {
    action: 'contact_submit'
});

// Backend (Python/Django)
response = requests.post('https://www.google.com/recaptcha/api/siteverify', {
    'secret': 'RECAPTCHA_SECRET_KEY',
    'response': recaptchaToken
})
```

### 3. Rate Limiting

- Frontend: 1 submission/30 seconds
- Backend: 5 submissions/hour/IP

## Implementation Steps

### Frontend Setup

1. Add reCAPTCHA script to page
2. Generate CSRF token on form submit
3. Collect form data and tokens
4. Send secure POST request

### Backend Requirements

1. Verify CSRF token match
2. Validate reCAPTCHA score (>0.5)
3. Check rate limits
4. Process valid submissions

## Security Flow

1. **Form Submit**
    - Validate inputs
    - Generate CSRF token
    - Get reCAPTCHA token
    - Bundle security payload

2. **API Request**
   ```typescript
   const submitForm = async (formData) => {
     const csrfToken = generateToken();
     const recaptchaToken = await getRecaptchaToken();
     
     const response = await fetch('/api/contact', {
       method: 'POST',
       headers: {
         'X-CSRF-Token': csrfToken
       },
       credentials: 'include',
       body: JSON.stringify({
         ...formData,
         recaptchaToken
       })
     });
   };
   ```

3. **Backend Validation**
   ```python
   def validate_request(request):
     # 1. CSRF Check
     if request.headers['X-CSRF-Token'] != request.cookies['x-csrf-token']:
       return False
       
     # 2. reCAPTCHA Check
     if not verify_recaptcha(request.data['recaptchaToken']):
       return False
       
     # 3. Rate Limit Check
     if is_rate_limited(request.ip):
       return False
       
     return True
   ```

## Why It's Secure

1. **CSRF Protection**
    - Token in both cookie and header
    - SameSite prevents cross-origin access
    - New token per submission

2. **Bot Prevention**
    - Invisible reCAPTCHA scoring
    - Rate limiting
    - Request validation

3. **Data Security**
    - HTTPS only
    - Input validation
    - No token storage

## Required Setup

### Frontend

```json
{
  "dependencies": {
    "crypto": "latest"
  }
}
```

### Backend

- reCAPTCHA secret key
- Rate limiting middleware
- Input validation
