from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from gabbro.organizations.urls import urlpatterns as organization_patterns
from gabbro.users.urls import urlpatterns as user_patterns
from gabbro.views import SentryGraphQLView
from graphene_django.views import GraphQLView

from app.views import TestView
from .schema import schema

GraphQLView.graphiql_template = "graphene_graphiql_explorer/graphiql.html"

urlpatterns = [
    path("", TestView.as_view()),
    path("i18n/", include("django.conf.urls.i18n"), name="set_language"),
    path("admin/", admin.site.urls),
    path(
        "graphql",
        csrf_exempt(SentryGraphQLView.as_view(schema=schema, graphiql=True)),
    ),
]

admin.site.site_header = _("GeoTech Website Admin")
admin.site.site_title = _("GeoTech Website Portal")
admin.site.index_title = _("Welcome to GeoTech Website Admin")

if settings.STORAGE_TYPE == "LOCAL":
    urlpatterns = urlpatterns + static("/static/", document_root=settings.STATIC_ROOT)
    urlpatterns = urlpatterns + static("/media/", document_root=settings.MEDIA_ROOT)

handler500 = "rest_framework.exceptions.server_error"
urlpatterns += organization_patterns
urlpatterns += user_patterns
