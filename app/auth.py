import logging

from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.middleware import get_user
from django.core.exceptions import ObjectDoesNotExist
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import gettext_lazy as _
from gabbro.middleware.hydra import TokenAuthentication as HydraTokenAuthentication
from rest_framework import exceptions


logger = logging.getLogger(__name__)


class TokenAuthentication(HydraTokenAuthentication):
    def handle_user(self, hydra_response, token):
        if not hydra_response.get("active"):
            logger.debug("The user provided a wrong or expired access_token")
            raise exceptions.AuthenticationFailed(_("INVALID TOKEN") % {})

        external_key = hydra_response.get("ext").get("external_key")
        email = hydra_response.get("ext").get("email")
        phone = hydra_response.get("ext").get("phone")
        first_name = hydra_response.get("ext").get("first_name")
        last_name = hydra_response.get("ext").get("last_name")

        # Getting the user if exists
        try:
            user = get_user_model().objects.get(external_key=external_key)
            user.email = email
            user.phone = phone
            user.first_name = first_name
            user.last_name = last_name
            user.save()
            user.refresh_from_db()

            logger.debug(
                "User with external_key < %s > is already registered", external_key
            )

        except ObjectDoesNotExist:
            logger.debug(
                "User with external_key < %s > is not registered, creating a new user",
                external_key,
            )
            user = get_user_model().objects.create_user(
                external_key=external_key,
                email=email,
                phone=phone,
                first_name=first_name,
                last_name=last_name,
            )
            logger.debug(
                "User with external_key < %s > is now registered", external_key
            )

        return user, token


class GraphQLTokenAuthentication(HydraTokenAuthentication):
    def handle_user(self, hydra_response, token):
        if not hydra_response.get("active"):
            logger.debug("The user provided a wrong or expired access_token")
            raise exceptions.AuthenticationFailed(_("INVALID TOKEN") % {})

        external_key = hydra_response.get("ext").get("external_key")
        email = hydra_response.get("ext").get("email")
        phone = hydra_response.get("ext").get("phone")
        first_name = hydra_response.get("ext").get("first_name")
        last_name = hydra_response.get("ext").get("last_name")
        avatar = hydra_response.get("ext").get("avatar")
        is_nafath_verified = hydra_response.get("ext").get("is_nafath_verified")

        # Getting the user if exists
        try:
            user = get_user_model().objects.get(external_key=external_key)
            user.email = email
            user.phone = phone
            user.first_name = first_name
            user.last_name = last_name
            user.avatar = avatar
            user.save()
            user.refresh_from_db()

            logger.debug(
                "User with external_key < %s > is already registered", external_key
            )

        except ObjectDoesNotExist:
            logger.debug(
                "User with external_key < %s > is not registered, creating a new user",
                external_key,
            )
            user = get_user_model().objects.create_user(
                external_key=external_key,
                email=email,
                phone=phone,
                first_name=first_name,
                last_name=last_name,
            )
            logger.debug(
                "User with external_key < %s > is now registered", external_key
            )

        return user


class GraphQLTokenAuthenticationMiddleware:
    def __init__(self):
        pass

    def resolve(self, next, root, info, **kwargs):
        context = info.context

        context.user = get_user(context)

        if context.user and not context.user.is_authenticated:
            context.user = authenticate(request=context, **kwargs)

        return next(root, info, **kwargs)


class HydraTokenAuthenticationMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # request.user = authenticate(request=request) or AnonymousUser()
        if "Authorization" in request.headers:
            try:
                user_obj = authenticate(request=request)
                if user_obj is not None:
                    request.user = user_obj
            except exceptions.AuthenticationFailed:
                pass
