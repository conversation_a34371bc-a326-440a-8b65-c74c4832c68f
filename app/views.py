from gabbro import __version__ as gabbro_version
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView


class TestView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        return Response(
            dict(
                message="GeoTech Website API GATEWAY : OK",
                geocore="0.1",
                gabbro=gabbro_version,
            )
        )
