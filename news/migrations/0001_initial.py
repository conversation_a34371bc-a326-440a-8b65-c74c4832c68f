# Generated by Django 3.2 on 2025-09-10 10:19

import django_extensions.db.fields
import phonenumber_field.modelfields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ContactUs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("name", models.CharField(max_length=128, verbose_name="Name")),
                ("email", models.EmailField(max_length=254, verbose_name="Email")),
                (
                    "phone",
                    phonenumber_field.modelfields.PhoneNumberField(
                        max_length=128, region=None, verbose_name="Phone"
                    ),
                ),
                (
                    "subject",
                    models.CharField(
                        blank=True, default="", max_length=128, verbose_name="Subject"
                    ),
                ),
                (
                    "message",
                    models.TextField(blank=True, default="", verbose_name="Message"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
