from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField


class ContactUs(TimeStampedModel):
    name = models.CharField(_("Name"), max_length=128)
    email = models.EmailField(_("Email"))
    phone = PhoneNumberField(_("Phone"))
    subject = models.CharField(_("Subject"), max_length=128, blank=True, default="")
    message = models.TextField(_("Message"), blank=True, default="")

    def __str__(self):
        return f"{self.name} - {self.email}"
