from django.contrib import admin
from gabbro.users.admin import UserAdmin

from users.models import User


class ExtendedUserAdmin(UserAdmin):
    list_display = (
        "id",
        "external_key",
        "email",
        "phone",
        "first_name",
        "last_name",
    )
    list_filter = ("is_superuser", "is_staff")
    search_fields = ["email", "phone", "first_name", "last_name"]

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(User, ExtendedUserAdmin)
