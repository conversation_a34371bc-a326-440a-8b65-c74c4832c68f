# Django and deploymenet specific
SECRET_KEY=strong-secret-value-is-a-must
DEBUG=True
LANGUAGE_CODE=en
PROJECT_NAME=gt-website-backend
# internal network mode True/False values
INTERNAL_MODE=True

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=gt_website_backend
DATABASE_USER=postgres
DATABASE_PASSWORD=passw0rd

# Accounts app
ACCOUNTS_BASE_URL=http://127.0.0.1:8001
ACCOUNTS_INTERNAL_BASE_URL=http://127.0.0.1:8001
ACCOUNTS_USERNAME=<EMAIL>
ACCOUNTS_PASSWORD=admin
# Hydra
HYDRA_ADMIN_URL=http://127.0.0.1:7001
HYDRA_PUBLIC_URL=http://127.0.0.1:7000
HYDRA_SSL_VERIFY=False

# Storage
STORAGE_TYPE=LOCAL
GC_PROJECT_ID=
GC_CREDENTIALS_INFO_PRIVATE_KEY_ID=
GC_CREDENTIALS_INFO_PRIVATE_KEY=
GC_CREDENTIALS_INFO_CLIENT_USERNAME=
GC_CREDENTIALS_INFO_CLIENT_ID=
GS_STATIC_BUCKET_NAME=
GS_MEDIA_BUCKET_NAME=
STATIC_URL=/static/
MEDIA_URL=/media/

# Elasticsearch
ES_HOST=https://localhost
ES_PORT=
ES_HTTP_AUTH=elastic:passw0rd

# Google
GOOGLE_API_KEY=

# -- Sentry --
SENTRY_DSN=
# LOCAL / DEVELOPMENT / STAGIGN / PRODUCTION
DEPLOYMENT=LOCAL

EMAIL_BACKEN=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.freesmtpservers.com
EMAIL_PORT=25
DEFAULT_FROM_EMAIL=<EMAIL>
TO_EMAILS=